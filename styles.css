/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #ffffff;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: 1rem;
}

a {
    color: inherit;
    text-decoration: none;
}

/* Header */
.header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(10px);
    z-index: 1000;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    padding: 1rem 0;
}

.nav-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.logo-text {
    font-size: 1.5rem;
    font-weight: 800;
    color: #1f2937;
}

.nav-menu {
    display: flex;
    align-items: center;
}

.nav-list {
    display: flex;
    list-style: none;
    gap: 2rem;
    align-items: center;
}

.nav-link {
    font-weight: 500;
    color: #374151;
    transition: color 0.3s ease;
}

.nav-link:hover {
    color: #4f46e5;
}

.nav-cta .cta-button {
    background: #4f46e5;
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: 6px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.nav-cta .cta-button:hover {
    background: #4338ca;
    transform: translateY(-1px);
}

.nav-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.bar {
    width: 25px;
    height: 3px;
    background: #374151;
    margin: 3px 0;
    transition: 0.3s;
}

/* Hero Section */
.hero {
    padding: 120px 0 60px;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

.hero-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.hero-title {
    font-size: 3rem;
    font-weight: 800;
    color: #1f2937;
    margin-bottom: 1rem;
    line-height: 1.1;
}

.hero-subtitle {
    margin-bottom: 1rem;
}

.highlight {
    background: #fef3c7;
    color: #92400e;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    font-weight: 600;
    font-size: 1.1rem;
}

.hero-description {
    font-size: 1.25rem;
    color: #6b7280;
    margin-bottom: 2rem;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.cta-button {
    display: inline-block;
    padding: 1rem 2rem;
    border-radius: 6px;
    font-weight: 600;
    font-size: 1rem;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    text-align: center;
}

.cta-button.primary {
    background: #4f46e5;
    color: white;
}

.cta-button.primary:hover {
    background: #4338ca;
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(79, 70, 229, 0.3);
}

.cta-button.secondary {
    background: transparent;
    color: #4f46e5;
    border: 2px solid #4f46e5;
}

.cta-button.secondary:hover {
    background: #4f46e5;
    color: white;
}

/* Hero Video */
.hero-video {
    display: flex;
    justify-content: center;
    align-items: center;
}

.video-placeholder {
    width: 100%;
    max-width: 500px;
    aspect-ratio: 16/9;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.video-placeholder iframe {
    width: 100%;
    height: 100%;
}

/* Trust Indicators */
.trust-indicators {
    background: white;
    padding: 2rem 0;
    border-top: 1px solid #e5e7eb;
}

.trust-indicators .container {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 4rem;
    flex-wrap: wrap;
}

.trust-text {
    font-weight: 600;
    color: #6b7280;
}

.rating {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.stars {
    color: #fbbf24;
    font-size: 1.2rem;
}

.rating-text {
    font-weight: 600;
    color: #6b7280;
}

/* Features Section */
.features {
    padding: 80px 0;
    background: white;
}

.section-title {
    font-size: 2.5rem;
    font-weight: 800;
    color: #1f2937;
    text-align: center;
    margin-bottom: 3rem;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.feature-card {
    background: #f9fafb;
    padding: 2rem;
    border-radius: 12px;
    border: 1px solid #e5e7eb;
    text-align: center;
    transition: all 0.3s ease;
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.feature-icon {
    margin-bottom: 1.5rem;
}

.placeholder-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
}

.feature-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 1rem;
}

.feature-description {
    color: #6b7280;
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.feature-list {
    list-style: none;
    text-align: left;
}

.feature-list li {
    color: #374151;
    margin-bottom: 0.5rem;
    position: relative;
    padding-left: 1.5rem;
}

.feature-list li:before {
    content: "✓";
    position: absolute;
    left: 0;
    color: #10b981;
    font-weight: bold;
}

.features-summary {
    text-align: center;
    margin-top: 3rem;
}

.features-summary p {
    font-size: 1.1rem;
    color: #6b7280;
    margin-bottom: 2rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

/* Results Section */
.results {
    padding: 80px 0;
    background: #f9fafb;
}

.results-header {
    text-align: center;
    margin-bottom: 4rem;
}

.section-subtitle {
    font-size: 1.25rem;
    color: #4f46e5;
    font-weight: 600;
    margin-bottom: 1rem;
}

.section-description {
    font-size: 1.1rem;
    color: #6b7280;
    max-width: 600px;
    margin: 0 auto;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.stat-card {
    background: white;
    padding: 2rem;
    border-radius: 12px;
    text-align: center;
    border: 1px solid #e5e7eb;
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.stat-number {
    font-size: 3rem;
    font-weight: 800;
    color: #4f46e5;
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 1rem;
    color: #6b7280;
    font-weight: 500;
}

.results-cta {
    text-align: center;
}

/* CTA Section */
.cta-section {
    padding: 80px 0;
    background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
    color: white;
    text-align: center;
}

.cta-title {
    font-size: 2.5rem;
    font-weight: 800;
    margin-bottom: 1rem;
}

.cta-subtitle {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.cta-description {
    font-size: 1.25rem;
    margin-bottom: 3rem;
    opacity: 0.9;
}

.offer-box {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 12px;
    padding: 2rem;
    max-width: 600px;
    margin: 0 auto;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.offer-title {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.offer-description {
    margin-bottom: 2rem;
    opacity: 0.9;
}

.offer-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

.offer-buttons .cta-button.primary {
    background: white;
    color: #4f46e5;
}

.offer-buttons .cta-button.primary:hover {
    background: #f3f4f6;
}

.offer-buttons .cta-button.secondary {
    background: transparent;
    color: white;
    border: 2px solid white;
}

.offer-buttons .cta-button.secondary:hover {
    background: white;
    color: #4f46e5;
}

/* Booking Section */
.booking-section {
    padding: 80px 0;
    background: white;
}

.calendly-placeholder {
    max-width: 800px;
    margin: 0 auto;
}

.calendly-embed {
    background: #f9fafb;
    border-radius: 12px;
    padding: 2rem;
    border: 1px solid #e5e7eb;
}

.calendly-mock {
    text-align: center;
}

.calendly-header h3 {
    font-size: 1.5rem;
    color: #1f2937;
    margin-bottom: 0.5rem;
}

.calendly-header p {
    color: #6b7280;
    margin-bottom: 2rem;
}

.calendar-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.calendar-day {
    background: white;
    border-radius: 8px;
    padding: 1rem;
    border: 1px solid #e5e7eb;
}

.day-header {
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #e5e7eb;
}

.time-slot {
    background: #f3f4f6;
    padding: 0.5rem;
    margin-bottom: 0.5rem;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.time-slot:hover {
    background: #4f46e5;
    color: white;
}

.calendly-footer p {
    color: #6b7280;
    margin-bottom: 1rem;
    font-style: italic;
}

.calendly-button {
    background: #4f46e5;
    color: white;
    padding: 1rem 2rem;
    border: none;
    border-radius: 6px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.calendly-button:hover {
    background: #4338ca;
}

/* Footer */
.footer {
    background: #1f2937;
    color: white;
    padding: 2rem 0;
    text-align: center;
}

.footer-content p {
    color: #9ca3af;
    font-size: 0.9rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-menu {
        position: fixed;
        left: -100%;
        top: 80px;
        flex-direction: column;
        background-color: white;
        width: 100%;
        text-align: center;
        transition: 0.3s;
        box-shadow: 0 10px 27px rgba(0, 0, 0, 0.05);
        padding: 2rem 0;
    }

    .nav-menu.active {
        left: 0;
    }

    .nav-list {
        flex-direction: column;
        gap: 1rem;
    }

    .nav-toggle {
        display: flex;
    }

    .hero-container {
        grid-template-columns: 1fr;
        gap: 2rem;
        text-align: center;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .section-title {
        font-size: 2rem;
    }

    .cta-title {
        font-size: 2rem;
    }

    .cta-subtitle {
        font-size: 1.5rem;
    }

    .hero-buttons {
        justify-content: center;
    }

    .offer-buttons {
        flex-direction: column;
        align-items: center;
    }

    .trust-indicators .container {
        flex-direction: column;
        gap: 1rem;
    }

    .calendar-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .hero-title {
        font-size: 2rem;
    }

    .section-title {
        font-size: 1.75rem;
    }

    .cta-title {
        font-size: 1.75rem;
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

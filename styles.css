/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

body {
    font-family: 'Maven Pro', sans-serif;
    line-height: 1.6;
    color: #333;
    overscroll-behavior: none;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 700;
    line-height: 1.2;
}

a {
    color: inherit;
    text-decoration: none;
}

/* Header */
.header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    z-index: 1000;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.nav-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 2rem;
    max-width: 1400px;
    margin: 0 auto;
}

.logo-text {
    font-size: 1.5rem;
    font-weight: 800;
    color: #2563eb;
}

.nav-menu {
    display: flex;
    align-items: center;
}

.nav-list {
    display: flex;
    list-style: none;
    gap: 2rem;
    align-items: center;
}

.nav-link {
    font-weight: 500;
    transition: color 0.3s ease;
}

.nav-link:hover {
    color: #2563eb;
}

.nav-dropdown {
    font-size: 0.875rem;
    color: #666;
    margin-left: 0.5rem;
}

.phone-link {
    font-weight: 600;
    color: #2563eb;
    font-size: 1.1rem;
}

.nav-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.bar {
    width: 25px;
    height: 3px;
    background: #333;
    margin: 3px 0;
    transition: 0.3s;
}

/* Hero Section */
.hero {
    padding: 120px 0 80px;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

.hero-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

.hero-content {
    text-align: center;
    margin-bottom: 4rem;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 800;
    color: #1e293b;
    margin-bottom: 1.5rem;
    line-height: 1.1;
}

.hero-subtitle {
    font-size: 1.25rem;
    color: #64748b;
    margin-bottom: 2rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.cta-button {
    display: inline-block;
    background: #2563eb;
    color: white;
    padding: 1rem 2rem;
    border-radius: 8px;
    font-weight: 600;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
}

.cta-button:hover {
    background: #1d4ed8;
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(37, 99, 235, 0.3);
}

.hero-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
    text-align: center;
}

.stat-item {
    padding: 1.5rem;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 800;
    color: #2563eb;
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 1rem;
    color: #64748b;
    font-weight: 500;
}

/* About Section */
.about {
    padding: 4rem 0;
    background: white;
}

.about-text {
    text-align: center;
    font-size: 1.25rem;
    color: #64748b;
    max-width: 800px;
    margin: 0 auto;
}

/* Services Intro */
.services-intro {
    padding: 4rem 0;
    background: #f8fafc;
}

.section-title {
    font-size: 2.5rem;
    font-weight: 800;
    color: #1e293b;
    margin-bottom: 1.5rem;
    text-align: center;
}

.section-subtitle {
    font-size: 1.25rem;
    color: #64748b;
    margin-bottom: 1.5rem;
    text-align: center;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
}

.section-text {
    font-size: 1.1rem;
    color: #64748b;
    margin-bottom: 2rem;
    text-align: center;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
}

.link-button {
    display: inline-block;
    color: #2563eb;
    font-weight: 600;
    text-decoration: underline;
    transition: color 0.3s ease;
}

.link-button:hover {
    color: #1d4ed8;
}

/* Services Grid */
.services {
    padding: 4rem 0;
    background: white;
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.service-card {
    padding: 2rem;
    border-radius: 12px;
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    transition: all 0.3s ease;
}

.service-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.service-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: #1e293b;
    margin-bottom: 1rem;
}

.service-description {
    color: #64748b;
    line-height: 1.6;
}

/* CTA Section */
.cta-section {
    padding: 4rem 0;
    background: #2563eb;
    text-align: center;
}

.cta-title {
    font-size: 2.5rem;
    font-weight: 800;
    color: white;
    margin-bottom: 2rem;
}

.cta-section .cta-button {
    background: white;
    color: #2563eb;
}

.cta-section .cta-button:hover {
    background: #f8fafc;
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(255, 255, 255, 0.3);
}

/* Features Section */
.features {
    padding: 4rem 0;
    background: white;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 3rem;
}

.feature-item {
    text-align: center;
}

.feature-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: #1e293b;
    margin-bottom: 1rem;
}

.feature-description {
    color: #64748b;
    line-height: 1.6;
}

/* Differentiator Section */
.differentiator {
    padding: 4rem 0;
    background: #f8fafc;
    text-align: center;
}

/* Testimonials Section */
.testimonials {
    padding: 4rem 0;
    background: white;
    text-align: center;
}

/* FAQ Section */
.faq {
    padding: 4rem 0;
    background: #f8fafc;
}

.faq-list {
    max-width: 800px;
    margin: 0 auto;
}

.faq-item {
    border-bottom: 1px solid #e2e8f0;
    margin-bottom: 1rem;
}

.faq-question {
    width: 100%;
    padding: 1.5rem 0;
    background: none;
    border: none;
    text-align: left;
    font-size: 1.1rem;
    font-weight: 600;
    color: #1e293b;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: color 0.3s ease;
}

.faq-question:hover {
    color: #2563eb;
}

.faq-icon {
    font-size: 1.5rem;
    font-weight: 300;
    transition: transform 0.3s ease;
}

.faq-item.active .faq-icon {
    transform: rotate(45deg);
}

.faq-answer {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
}

.faq-item.active .faq-answer {
    max-height: 500px;
}

.faq-answer p {
    padding-bottom: 1.5rem;
    color: #64748b;
    line-height: 1.6;
}

.faq-answer ul {
    padding-left: 1.5rem;
    margin-bottom: 1.5rem;
}

.faq-answer li {
    color: #64748b;
    margin-bottom: 0.5rem;
}

/* Contact CTA */
.contact-cta {
    padding: 4rem 0;
    background: white;
    text-align: center;
}

/* Services Tags */
.services-tags {
    padding: 4rem 0;
    background: #f8fafc;
}

.tags-grid {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    justify-content: center;
}

.tag {
    background: white;
    color: #64748b;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 500;
    border: 1px solid #e2e8f0;
    transition: all 0.3s ease;
}

.tag:hover {
    background: #2563eb;
    color: white;
    border-color: #2563eb;
}

/* Blog Section */
.blog {
    padding: 4rem 0;
    background: white;
}

.blog-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.blog-card {
    padding: 2rem;
    border-radius: 12px;
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    transition: all 0.3s ease;
}

.blog-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.blog-date {
    font-size: 0.875rem;
    color: #64748b;
    margin-bottom: 1rem;
}

.blog-title {
    font-size: 1.25rem;
    font-weight: 700;
    color: #1e293b;
    margin-bottom: 1rem;
    line-height: 1.3;
}

.blog-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.blog-tag {
    background: #e2e8f0;
    color: #64748b;
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
}

/* Footer */
.footer {
    background: #1e293b;
    color: white;
    padding: 4rem 0 2rem;
}

.footer-content {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 4rem;
    margin-bottom: 3rem;
}

.footer-description p {
    color: #94a3b8;
    margin-bottom: 2rem;
    line-height: 1.6;
}

.footer-legal {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.footer-legal a {
    color: #94a3b8;
    font-size: 0.875rem;
}

.footer-legal span {
    color: #64748b;
}

.footer-links {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 2rem;
}

.footer-title {
    font-size: 0.875rem;
    font-weight: 700;
    color: white;
    margin-bottom: 1rem;
    letter-spacing: 0.05em;
}

.footer-list {
    list-style: none;
}

.footer-list li {
    margin-bottom: 0.5rem;
}

.footer-list a {
    color: #94a3b8;
    font-size: 0.875rem;
    transition: color 0.3s ease;
}

.footer-list a:hover {
    color: white;
}

.footer-hours {
    margin-top: 1.5rem;
}

.footer-hours h5 {
    font-size: 0.875rem;
    font-weight: 700;
    color: white;
    margin-bottom: 0.5rem;
    letter-spacing: 0.05em;
}

.footer-hours p {
    color: #94a3b8;
    font-size: 0.875rem;
}

.footer-bottom {
    border-top: 1px solid #334155;
    padding-top: 2rem;
    text-align: center;
}

.footer-bottom p {
    color: #94a3b8;
    font-size: 0.875rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-menu {
        position: fixed;
        left: -100%;
        top: 70px;
        flex-direction: column;
        background-color: white;
        width: 100%;
        text-align: center;
        transition: 0.3s;
        box-shadow: 0 10px 27px rgba(0, 0, 0, 0.05);
        padding: 2rem 0;
    }

    .nav-menu.active {
        left: 0;
    }

    .nav-list {
        flex-direction: column;
        gap: 1rem;
    }

    .nav-toggle {
        display: flex;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .section-title {
        font-size: 2rem;
    }

    .cta-title {
        font-size: 2rem;
    }

    .footer-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .footer-links {
        grid-template-columns: repeat(2, 1fr);
    }

    .hero-stats {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 480px) {
    .hero-title {
        font-size: 2rem;
    }

    .section-title {
        font-size: 1.75rem;
    }

    .cta-title {
        font-size: 1.75rem;
    }

    .hero-stats {
        grid-template-columns: 1fr;
    }

    .footer-links {
        grid-template-columns: 1fr;
    }
}
